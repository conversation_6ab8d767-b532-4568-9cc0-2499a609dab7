//
//  BreathCounterView.swift
//  Breath2
//
//  Displays breath count and current breath status
//

import SwiftUI

struct BreathCounterView: View {
    @ObservedObject var breathDetector: BreathDetector
    
    var body: some View {
        VStack(spacing: 12) {
            // Main breath counter
            breathCountDisplay
            
            // Current breath status
            currentBreathStatus
            
            // Last breath info (if available)
            if let lastBreath = breathDetector.lastBreathEvent {
                lastBreathInfo(lastBreath)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.2))
                .stroke(breathDetector.isBreathing ? Color.green : Color.gray.opacity(0.3), lineWidth: 2)
        )
        .animation(.easeInOut(duration: 0.3), value: breathDetector.isBreathing)
    }
    
    // MARK: - Breath Count Display
    
    private var breathCountDisplay: some View {
        VStack(spacing: 4) {
            Text("Breaths")
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
            
            Text("\(breathDetector.breathCount)")
                .font(.system(size: 48, weight: .bold, design: .rounded))
                .foregroundColor(breathDetector.isBreathing ? .green : .white)
                .animation(.easeInOut(duration: 0.3), value: breathDetector.breathCount)
        }
    }
    
    // MARK: - Current Breath Status
    
    private var currentBreathStatus: some View {
        VStack(spacing: 8) {
            // Status indicator
            HStack(spacing: 8) {
                Circle()
                    .fill(statusColor)
                    .frame(width: 8, height: 8)
                    .scaleEffect(breathDetector.isBreathing ? 1.3 : 1.0)
                    .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), 
                              value: breathDetector.isBreathing)
                
                Text(breathDetector.statusDescription)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            // Current breath duration (if breathing)
            if breathDetector.isBreathing && breathDetector.currentBreathDuration > 0 {
                HStack(spacing: 4) {
                    Text("Duration:")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.7))
                    
                    Text("\(breathDetector.currentDurationText)s")
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(breathDetector.isInValidBreath ? .green : .yellow)
                }
            }
        }
    }
    
    // MARK: - Last Breath Info
    
    private func lastBreathInfo(_ breath: BreathEvent) -> some View {
        VStack(spacing: 4) {
            Text("Last Breath")
                .font(.caption2)
                .foregroundColor(.white.opacity(0.7))
            
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Duration")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.7))
                    Text("\(String(format: "%.1f", breath.duration))s")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(breath.isValid ? .green : .orange)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Quality")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.7))
                    Text(breathQualityText(breath))
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(breathQualityColor(breath))
                }
            }
        }
        .padding(.top, 4)
    }
    
    // MARK: - Helper Properties
    
    private var statusColor: Color {
        switch breathDetector.currentState {
        case .idle:
            return .gray
        case .starting:
            return .yellow
        case .active:
            return .green
        case .ending:
            return .orange
        case .completed:
            return .blue
        }
    }
    
    private func breathQualityText(_ breath: BreathEvent) -> String {
        if breath.duration >= 3.0 {
            return "Excellent"
        } else if breath.duration >= 2.0 {
            return "Good"
        } else if breath.duration >= 1.5 {
            return "Fair"
        } else {
            return "Short"
        }
    }
    
    private func breathQualityColor(_ breath: BreathEvent) -> Color {
        if breath.duration >= 3.0 {
            return .green
        } else if breath.duration >= 2.0 {
            return .blue
        } else if breath.duration >= 1.5 {
            return .yellow
        } else {
            return .orange
        }
    }
}

// MARK: - Compact Breath Counter

struct CompactBreathCounterView: View {
    @ObservedObject var breathDetector: BreathDetector

    var body: some View {
        // Just show the breath count number
        Text("\(breathDetector.breathCount)")
            .font(.title2)
            .fontWeight(.bold)
            .foregroundColor(breathDetector.isBreathing ? .green : .white)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.black.opacity(0.2))
                    .stroke(breathDetector.isBreathing ? Color.green.opacity(0.5) : Color.gray.opacity(0.3), lineWidth: 1)
            )
    }
}

// MARK: - Preview

struct BreathCounterView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            BreathCounterView(breathDetector: BreathDetector())
            
            CompactBreathCounterView(breathDetector: BreathDetector())
        }
        .padding()
        .background(Color.black)
        .previewLayout(.sizeThatFits)
    }
}
