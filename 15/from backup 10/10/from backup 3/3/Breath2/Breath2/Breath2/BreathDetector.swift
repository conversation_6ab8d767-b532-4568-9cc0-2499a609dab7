//
//  BreathDetector.swift
//  Breath2
//
//  Simple, reliable breath start/end detection based on oscillation presence
//

import Foundation
import SwiftUI

// MARK: - Breath Detection State

enum BreathState {
    case idle           // No breathing detected
    case starting       // Oscillations detected, but too short
    case active         // Valid breath in progress (>1.5s)
    case ending         // Oscillations stopped, checking if valid
    case completed      // Valid breath completed
}

// MARK: - Breath Event

struct BreathEvent: Equatable {
    let breathNumber: Int
    let startTime: Date
    let endTime: Date
    let duration: TimeInterval
    let averageAmplitude: Float
    let maxAmplitude: Float

    var isValid: Bool {
        return duration >= 1.5 // Minimum 1.5 seconds
    }

    // Equatable conformance
    static func == (lhs: BreathEvent, rhs: BreathEvent) -> Bool {
        return lhs.breathNumber == rhs.breathNumber &&
               lhs.startTime == rhs.startTime &&
               lhs.endTime == rhs.endTime
    }
}

// MARK: - Breath Detector

class BreathDetector: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentState: BreathState = .idle
    @Published var breathCount: Int = 0
    @Published var currentBreathDuration: TimeInterval = 0
    @Published var isBreathing: Bool = false
    @Published var lastBreathEvent: BreathEvent?
    
    // MARK: - Detection Parameters

    /// Minimum audio level to consider as oscillation
    private let oscillationThreshold: Float = 0.005

    /// Minimum pressure to consider as breathing signal (cm H2O)
    private let minimumPressureThreshold: Float = 3.0

    /// Minimum frequency to consider as breathing signal (Hz)
    private let minimumFrequencyThreshold: Float = 5.0

    /// Minimum duration to count as valid breath
    private let minimumBreathDuration: TimeInterval = 1.5

    /// Maximum gap in oscillations before considering breath ended
    private let maxSilenceGap: TimeInterval = 0.3

    /// Minimum time between breaths to avoid double-counting
    private let minimumBreathInterval: TimeInterval = 0.5
    
    // MARK: - Internal State

    private var currentBreathStartTime: Date?
    private var lastOscillationTime: Date?
    private var amplitudeHistory: [Float] = []
    private var lastBreathEndTime: Date?
    private var currentBreathWasActive: Bool = false
    
    // MARK: - Detection Logic
    
    /// Process audio level and detect breath events
    func processAudioLevel(_ audioLevel: Float, timestamp: Date = Date()) {
        let hasOscillation = audioLevel > oscillationThreshold

        // Update oscillation tracking
        if hasOscillation {
            lastOscillationTime = timestamp
            amplitudeHistory.append(audioLevel)

            // Keep amplitude history manageable
            if amplitudeHistory.count > 100 {
                amplitudeHistory.removeFirst(50)
            }
        }

        // State machine for breath detection
        updateBreathState(hasOscillation: hasOscillation, timestamp: timestamp)

        // Update current breath duration
        updateCurrentDuration(timestamp: timestamp)
    }

    /// Process pressure and frequency readings for breath detection (preferred method)
    func processBreathingSignals(pressure: Float, frequency: Float, audioLevel: Float, timestamp: Date = Date()) {
        // Use pressure and frequency to detect meaningful breathing
        // A breath is detected when we have sustained pressure/frequency readings OR significant audio
        let hasPressureSignal = pressure >= minimumPressureThreshold
        let hasFrequencySignal = frequency >= minimumFrequencyThreshold
        let hasAudioSignal = audioLevel > oscillationThreshold

        // Breathing signal is detected if we have both pressure AND frequency, OR strong audio
        let hasBreathingSignal = (hasPressureSignal && hasFrequencySignal) || hasAudioSignal

        // Update oscillation tracking with breathing signal
        if hasBreathingSignal {
            lastOscillationTime = timestamp
            // Use pressure as amplitude for better breath quality measurement
            let breathAmplitude = max(pressure / 15.0, audioLevel) // Normalize pressure to 0-1 range (15 cm H2O max)
            amplitudeHistory.append(breathAmplitude)

            // Keep amplitude history manageable
            if amplitudeHistory.count > 100 {
                amplitudeHistory.removeFirst(50)
            }
        }

        // State machine for breath detection
        updateBreathState(hasOscillation: hasBreathingSignal, timestamp: timestamp)

        // Update current breath duration
        updateCurrentDuration(timestamp: timestamp)
    }
    
    private func updateBreathState(hasOscillation: Bool, timestamp: Date) {
        switch currentState {
        case .idle:
            if hasOscillation {
                startNewBreath(timestamp: timestamp)
            }
            
        case .starting:
            if hasOscillation {
                // Continue building breath
                checkForActiveBreath(timestamp: timestamp)
            } else {
                // Check if silence gap is too long
                checkForBreathEnd(timestamp: timestamp)
            }
            
        case .active:
            if hasOscillation {
                // Continue active breath
                isBreathing = true
            } else {
                // Check if silence gap is too long
                checkForBreathEnd(timestamp: timestamp)
            }
            
        case .ending:
            if hasOscillation {
                // Oscillations resumed, back to active
                currentState = .active
                isBreathing = true
            } else {
                // Check if enough time has passed to confirm end
                confirmBreathEnd(timestamp: timestamp)
            }
            
        case .completed:
            // Brief state, immediately return to idle
            currentState = .idle
            isBreathing = false
        }
    }
    
    private func startNewBreath(timestamp: Date) {
        // Prevent double-counting if too soon after last breath
        if let lastEnd = lastBreathEndTime,
           timestamp.timeIntervalSince(lastEnd) < minimumBreathInterval {
            return
        }

        currentBreathStartTime = timestamp
        amplitudeHistory.removeAll()
        amplitudeHistory.append(oscillationThreshold)
        currentState = .starting
        currentBreathWasActive = false
        isBreathing = true

        print("🫁 Breath detection: Starting new breath")
    }
    
    private func checkForActiveBreath(timestamp: Date) {
        guard let startTime = currentBreathStartTime else { return }

        let duration = timestamp.timeIntervalSince(startTime)
        if duration >= minimumBreathDuration {
            currentState = .active
            currentBreathWasActive = true
            print("🫁 Breath detection: Breath now active (duration: \(String(format: "%.1f", duration))s)")
        }
    }
    
    private func checkForBreathEnd(timestamp: Date) {
        guard let lastOscillation = lastOscillationTime else {
            // No oscillations detected yet, end immediately
            endCurrentBreath(timestamp: timestamp, wasValid: false)
            return
        }
        
        let silenceGap = timestamp.timeIntervalSince(lastOscillation)
        if silenceGap > maxSilenceGap {
            currentState = .ending
            print("🫁 Breath detection: Entering ending state (silence: \(String(format: "%.1f", silenceGap))s)")
        }
    }
    
    private func confirmBreathEnd(timestamp: Date) {
        guard let lastOscillation = lastOscillationTime else {
            endCurrentBreath(timestamp: timestamp, wasValid: false)
            return
        }

        let silenceGap = timestamp.timeIntervalSince(lastOscillation)
        if silenceGap > maxSilenceGap {
            let wasValid = currentBreathWasActive // Use tracked flag instead of current state
            endCurrentBreath(timestamp: lastOscillation, wasValid: wasValid)
        }
    }
    
    private func endCurrentBreath(timestamp: Date, wasValid: Bool) {
        guard let startTime = currentBreathStartTime else { return }
        
        let duration = timestamp.timeIntervalSince(startTime)
        let avgAmplitude = amplitudeHistory.isEmpty ? 0 : amplitudeHistory.reduce(0, +) / Float(amplitudeHistory.count)
        let maxAmplitude = amplitudeHistory.max() ?? 0
        
        // Create breath event
        let breathEvent = BreathEvent(
            breathNumber: breathCount + 1,
            startTime: startTime,
            endTime: timestamp,
            duration: duration,
            averageAmplitude: avgAmplitude,
            maxAmplitude: maxAmplitude
        )
        
        // Only count valid breaths
        if wasValid && breathEvent.isValid {
            breathCount += 1
            lastBreathEvent = breathEvent
            print("🫁 Breath detection: Completed breath #\(breathCount) (duration: \(String(format: "%.1f", duration))s)")
        } else {
            print("🫁 Breath detection: Discarded invalid breath (duration: \(String(format: "%.1f", duration))s)")
        }
        
        // Reset state
        currentState = .completed
        lastBreathEndTime = timestamp
        currentBreathStartTime = nil
        lastOscillationTime = nil
        amplitudeHistory.removeAll()
        currentBreathWasActive = false
        isBreathing = false
        
        // Immediately transition to idle
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if self.currentState == .completed {
                self.currentState = .idle
            }
        }
    }
    
    private func updateCurrentDuration(timestamp: Date) {
        if let startTime = currentBreathStartTime {
            currentBreathDuration = timestamp.timeIntervalSince(startTime)
        } else {
            currentBreathDuration = 0
        }
    }
    
    // MARK: - Public Methods
    
    /// Reset all breath detection state
    func reset() {
        currentState = .idle
        breathCount = 0
        currentBreathDuration = 0
        isBreathing = false
        lastBreathEvent = nil
        currentBreathStartTime = nil
        lastOscillationTime = nil
        lastBreathEndTime = nil
        currentBreathWasActive = false
        amplitudeHistory.removeAll()

        print("🫁 Breath detection: Reset")
    }
    
    /// Get current breath statistics
    func getCurrentBreathStats() -> (duration: TimeInterval, amplitude: Float) {
        let avgAmplitude = amplitudeHistory.isEmpty ? 0 : amplitudeHistory.reduce(0, +) / Float(amplitudeHistory.count)
        return (currentBreathDuration, avgAmplitude)
    }
    
    /// Check if currently in a valid breathing state
    var isInValidBreath: Bool {
        return currentState == .active || (currentState == .starting && currentBreathDuration >= minimumBreathDuration)
    }
    
    /// Get breath detection status for UI
    var statusDescription: String {
        switch currentState {
        case .idle:
            return "Ready to breathe"
        case .starting:
            return "Breath starting..."
        case .active:
            return "Breathing active"
        case .ending:
            return "Breath ending..."
        case .completed:
            return "Breath completed"
        }
    }
}

// MARK: - Breath Detection Extensions

extension BreathDetector {
    
    /// Get formatted breath count for display
    var breathCountText: String {
        return "\(breathCount)"
    }
    
    /// Get formatted current duration for display
    var currentDurationText: String {
        return String(format: "%.1f", currentBreathDuration)
    }
    
    /// Get last breath duration for display
    var lastBreathDurationText: String {
        guard let lastBreath = lastBreathEvent else { return "--" }
        return String(format: "%.1f", lastBreath.duration)
    }
}
