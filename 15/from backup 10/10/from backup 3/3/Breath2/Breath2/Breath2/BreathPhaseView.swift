//
//  BreathPhaseView.swift
//  Breath2
//
//  Breath-aware UI that accommodates detection delay
//

import SwiftUI

struct BreathPhaseView: View {
    @ObservedObject var phaseManager: BreathPhaseManager
    @ObservedObject var audioManager: AudioManager
    
    var body: some View {
        VStack(spacing: 30) {
            // Stable main guidance
            stableGuidanceView

            // Central pressure display
            centralPressureView

            // Minimal status indicator
            statusIndicatorView
        }
        .padding(30)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.black.opacity(0.3))
        )
        .animation(.easeInOut(duration: 0.8), value: phaseManager.isInTargetRange)
    }
    
    // MARK: - Stable Guidance View

    private var stableGuidanceView: some View {
        VStack(spacing: 12) {
            // Single, stable instruction
            Text(stableInstructionText)
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .animation(.easeInOut(duration: 0.5), value: stableInstructionText)

            // Target range (always visible)
            Text("Target: \(Int(phaseManager.targetPressureRange.lowerBound))-\(Int(phaseManager.targetPressureRange.upperBound)) cm H2O")
                .font(.subheadline)
                .foregroundColor(.gray)
        }
    }

    private var stableInstructionText: String {
        // Only change text when really necessary
        if phaseManager.currentPhase == .idle {
            return "Breathe steadily for 3-4 seconds"
        } else if audioManager.currentPressure == 0 {
            return "Breathe steadily for 3-4 seconds"
        } else if phaseManager.isInTargetRange {
            return "Perfect - maintain this pressure"
        } else if audioManager.currentPressure < Double(phaseManager.targetPressureRange.lowerBound) {
            return "Breathe a little harder"
        } else {
            return "Ease up slightly"
        }
    }
    
    // MARK: - Central Pressure Display

    private var centralPressureView: some View {
        ZStack {
            // Stable background circle
            Circle()
                .stroke(Color.gray.opacity(0.3), lineWidth: 8)
                .frame(width: 160, height: 160)

            // Target range indicator (always visible)
            Circle()
                .trim(from: 0.25, to: 0.75) // Half circle for target range
                .stroke(Color.green.opacity(0.4), lineWidth: 12)
                .frame(width: 160, height: 160)
                .rotationEffect(.degrees(-90))

            // Current pressure indicator (smooth updates)
            if audioManager.currentPressure > 0 {
                Circle()
                    .trim(from: 0, to: min(CGFloat(audioManager.currentPressure / 30.0), 1.0))
                    .stroke(
                        phaseManager.isInTargetRange ? Color.green : Color.cyan,
                        lineWidth: 8
                    )
                    .frame(width: 160, height: 160)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.3), value: audioManager.currentPressure)
            }

            // Center breath information
            VStack(spacing: 6) {
                // Breath count (main display)
                Text("\(audioManager.breathDetector.breathCount)")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(audioManager.breathDetector.isBreathing ? .green : .white)
                    .contentTransition(.numericText())

                Text("Breaths")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .fontWeight(.medium)

                // Current breath timer (if breathing)
                if audioManager.breathDetector.isBreathing && audioManager.breathDetector.currentBreathDuration > 0 {
                    Text(formatBreathDuration(audioManager.breathDetector.currentBreathDuration))
                        .font(.system(size: 16, weight: .semibold, design: .monospaced))
                        .foregroundColor(.cyan)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(Color.cyan.opacity(0.1))
                        )
                }

                // Breath status
                HStack(spacing: 6) {
                    Circle()
                        .fill(breathStatusColor)
                        .frame(width: 8, height: 8)
                        .scaleEffect(audioManager.breathDetector.isBreathing ? 1.3 : 1.0)
                        .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true),
                                  value: audioManager.breathDetector.isBreathing)

                    Text(breathStatusText)
                        .font(.caption2)
                        .foregroundColor(.gray)
                }
            }
        }
    }
    
    // MARK: - Status Indicator

    private var statusIndicatorView: some View {
        HStack(spacing: 16) {
            // Simple breath progress (only during active breathing)
            if phaseManager.breathDuration > 0 && phaseManager.breathDuration < 5.0 {
                VStack(spacing: 4) {
                    Text("Breath Progress")
                        .font(.caption)
                        .foregroundColor(.gray)

                    ProgressView(value: min(phaseManager.breathDuration / 3.5, 1.0))
                        .progressViewStyle(LinearProgressViewStyle(tint: .cyan))
                        .frame(width: 100)
                }
            }

            Spacer()

            // Simple status dot
            HStack(spacing: 8) {
                Circle()
                    .fill(statusColor)
                    .frame(width: 8, height: 8)

                Text(statusText)
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
    }

    private var statusColor: Color {
        if audioManager.currentPressure == 0 {
            return .gray
        } else {
            return .green
        }
    }

    private var statusText: String {
        if audioManager.currentPressure == 0 {
            return "Ready"
        } else {
            return "Active"
        }
    }

    private var breathStatusColor: Color {
        switch audioManager.breathDetector.currentState {
        case .idle: return .gray
        case .starting: return .yellow
        case .active: return .green
        case .ending: return .orange
        case .completed: return .blue
        }
    }

    private var breathStatusText: String {
        switch audioManager.breathDetector.currentState {
        case .idle: return "Ready"
        case .starting: return "Starting"
        case .active: return "Active"
        case .ending: return "Ending"
        case .completed: return "Done"
        }
    }

    private func formatBreathDuration(_ duration: TimeInterval) -> String {
        return String(format: "%.1fs", duration)
    }

}

// MARK: - Preview

struct BreathPhaseView_Previews: PreviewProvider {
    static var previews: some View {
        let phaseManager = BreathPhaseManager()
        let audioManager = AudioManager()

        BreathPhaseView(phaseManager: phaseManager, audioManager: audioManager)
            .padding()
            .previewLayout(.sizeThatFits)
    }
}
